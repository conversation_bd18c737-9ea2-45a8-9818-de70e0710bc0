# CRM integration utilities
import requests
from typing import Dict, List, Optional
import config

class CRMIntegration:
    """Integration with CRM system for customer data."""
    
    def __init__(self):
        self.api_key = config.CRM_API_KEY
        self.base_url = "https://api.crm-system.com/v1"  # Replace with actual CRM API URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_customer_info(self, customer_id: str) -> Optional[Dict]:
        """Retrieve customer information from CRM."""
        try:
            url = f"{self.base_url}/customers/{customer_id}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"Error fetching customer info: {str(e)}")
            return None
    
    def search_customers(self, query: str) -> List[Dict]:
        """Search for customers in CRM."""
        try:
            url = f"{self.base_url}/customers/search"
            params = {"q": query}
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                return response.json().get("customers", [])
            else:
                return []
                
        except Exception as e:
            print(f"Error searching customers: {str(e)}")
            return []
    
    def create_lead(self, lead_data: Dict) -> Optional[Dict]:
        """Create a new lead in CRM."""
        try:
            url = f"{self.base_url}/leads"
            response = requests.post(url, headers=self.headers, json=lead_data)
            
            if response.status_code == 201:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"Error creating lead: {str(e)}")
            return None
    
    def update_customer(self, customer_id: str, update_data: Dict) -> bool:
        """Update customer information in CRM."""
        try:
            url = f"{self.base_url}/customers/{customer_id}"
            response = requests.put(url, headers=self.headers, json=update_data)
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"Error updating customer: {str(e)}")
            return False
