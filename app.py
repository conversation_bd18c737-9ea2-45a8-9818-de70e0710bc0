import streamlit as st
import os
from datetime import datetime
from utils.document_loader import DocumentLoader
from utils.chat_engine import ChatEngine
from utils.crm_integration import HubSpotIntegration
from utils.calendar_integration import GoogleCalendarIntegration
from config import Config

# Page configuration
st.set_page_config(
    page_title="FrameIQ AI Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "chat_engine" not in st.session_state:
    st.session_state.chat_engine = None
if "user_info" not in st.session_state:
    st.session_state.user_info = {}

# Sidebar
with st.sidebar:
    st.title("🤖 FrameIQ Assistant")
    st.markdown("---")
    
    # User type selection
    user_type = st.radio(
        "I am a:",
        ["Industrial Client", "Student", "General Visitor"],
        key="user_type"
    )
    
    # User information form
    st.subheader("Your Information")
    name = st.text_input("Name", key="user_name")
    email = st.text_input("Email", key="user_email")
    company = st.text_input("Company/Institution", key="user_company")
    
    if st.button("Save Information"):
        st.session_state.user_info = {
            "name": name,
            "email": email,
            "company": company,
            "user_type": user_type
        }
        st.success("Information saved!")
    
    st.markdown("---")
    
    # Admin section
    with st.expander("Admin Dashboard"):
        st.metric("Total Conversations", len(st.session_state.messages))
        if st.button("Export Chat History"):
            # Export functionality
            st.download_button(
                label="Download Chat Log",
                data=str(st.session_state.messages),
                file_name=f"chat_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )
    
    # Clear chat button
    if st.button("Clear Chat History"):
        st.session_state.messages = []
        st.rerun()

# Main chat interface
st.title("Welcome to FrameIQ AI Assistant")
st.caption("Ask me anything about FrameIQ's products, services, or schedule a meeting!")

# Initialize chat engine
@st.cache_resource
def initialize_chat_engine():
    config = Config()
    loader = DocumentLoader(config.DATA_PATH)
    engine = ChatEngine(config)
    engine.initialize(loader.load_documents())
    return engine

if st.session_state.chat_engine is None:
    with st.spinner("Initializing AI Assistant..."):
        st.session_state.chat_engine = initialize_chat_engine()

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.write(message["content"])

# Chat input
if prompt := st.chat_input("How can I help you today?"):
    # Add user message to history
    st.session_state.messages.append({"role": "user", "content": prompt})
    
    # Display user message
    with st.chat_message("user"):
        st.write(prompt)
    
    # Generate response
    with st.chat_message("assistant"):
        with st.spinner("Thinking..."):
            # Check for special intents
            if "schedule" in prompt.lower() or "meeting" in prompt.lower():
                # Handle meeting scheduling
                response = handle_meeting_request(prompt)
            elif "contact" in prompt.lower() or "reach out" in prompt.lower():
                # Handle contact request
                response = handle_contact_request()
            else:
                # Regular chat response
                response = st.session_state.chat_engine.chat(
                    prompt, 
                    user_type=st.session_state.get("user_type", "General Visitor")
                )
            
            st.write(response)
    
    # Add assistant message to history
    st.session_state.messages.append({"role": "assistant", "content": response})

# Helper functions
def handle_meeting_request(prompt):
    """Handle meeting scheduling requests"""
    if not st.session_state.user_info.get("email"):
        return "To schedule a meeting, I'll need your contact information. Please fill out the form in the sidebar first."
    
    # Here you would integrate with Google Calendar
    return f"I'd be happy to help you schedule a meeting! Our team will reach out to {st.session_state.user_info['email']} within 24 hours to coordinate a suitable time."

def handle_contact_request():
    """Handle contact/lead generation requests"""
    if st.session_state.user_info.get("email"):
        # Create lead in HubSpot
        hubspot = HubSpotIntegration()
        result = hubspot.create_contact(st.session_state.user_info)
        if result:
            return "Thank you for your interest! Our team will contact you shortly."
    return "Please provide your contact information in the sidebar so we can reach out to you."

# Footer
st.markdown("---")
st.caption("Powered by FrameIQ AI | Built with Streamlit, Groq, and LlamaIndex")
