import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API Keys
    GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
    HUBSPOT_API_KEY = os.getenv("HUBSPOT_API_KEY", "")
    GOOGLE_CALENDAR_CREDENTIALS = os.getenv("GOOGLE_CALENDAR_CREDENTIALS", "")
    
    # Model Configuration
    LLM_MODEL = "llama3-70b-8192"
    EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
    
    # Paths
    DATA_PATH = "./data/company_data"
    VECTOR_STORE_PATH = "./chroma_db"
    
    # Chat Configuration
    MAX_CHAT_HISTORY = 10
    TEMPERATURE = 0.7
    MAX_TOKENS = 1024
    
    # System Prompts
    SYSTEM_PROMPT_TEMPLATE = """You are FrameIQ's AI assistant. You help {user_type} with information about our products and services.
    
    Key guidelines:
    - Be professional, helpful, and concise
    - If asked about scheduling meetings, collect contact information first
    - For technical questions, provide detailed explanations
    - For students, focus on educational aspects and learning opportunities
    - For industrial clients, emphasize business value and ROI
    
    Context: {context}
    
    Question: {question}
    """
