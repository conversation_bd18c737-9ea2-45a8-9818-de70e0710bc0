# ! pip install --upgrade llama-index llama-index-embeddings-huggingface

! pip install llama-index-vector-stores-faiss

# ! pip install llama-index-embeddings-huggingface==0.1.0

# ! pip install llama-index-vector-stores-faiss

# ! pip install --upgrade llama-index llama-index-embeddings-huggingface

! pip install --upgrade pydantic

from llama_index.vector_stores.faiss import FaissVectorStore
from llama_index.core import VectorStoreIndex,load_index_from_storage, StorageContext
from llama_index.readers.file import PyMuPDFReader
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
import faiss

# 

#data loading
loader = PyMuPDFReader()
documents = loader.load(file_path="FrameIQ dummy.pdf")

documents

from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.core import Settings


embed_model = HuggingFaceEmbedding(
    model_name="BAAI/bge-small-en-v1.5"
)

# ! pip show llama-index

#faiss database with dimension
d = 384
faiss_index = faiss.IndexFlatL2(d)
vector_store = FaissVectorStore(faiss_index=faiss_index)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# storing the data after embeddings 

index = VectorStoreIndex.from_documents(
    documents,
    storage_context=storage_context,
    embed_model=embed_model
)


# save index to disk
index.storage_context.persist()

# load index from disk
vector_store = FaissVectorStore.from_persist_dir("storage")
storage_context = StorageContext.from_defaults(
    vector_store=vector_store, persist_dir="storage"
)
index = load_index_from_storage(storage_context=storage_context, embed_model=embed_model)

! pip install llama-index-llms-groq

! pip install --upgrade llama-index llama-index-llms-groq


! pip install --upgrade llama-index pydantic


from llama_index.llms.groq import Groq

import getpass

api_key = getpass.getpass("Enter your Groq API key: ")


llm = Groq(model="llama-3.1-8b-instant", api_key= api_key)

query_engine = index.as_query_engine(llm = llm, similarity_top_k=3)

# Test the query engine
response = query_engine.query("tell me about frameIQ")
print(response)

! pip install llama-index-core-agent

# from llama_index.core.agent.workflow import FunctionAgent


from llama_index.core.tools import QueryEngineTool

# Wrap the query_engine in a QueryEngineTool
frameiq_tool = QueryEngineTool.from_defaults(
    query_engine=query_engine,
    name="frameiq_tool",
    description="A tool to retrieve information about FrameIQ from the indexed data."
)


# # Create an enhanced workflow with both tools
# agent = FunctionAgent(
#     tools=[query_engine],
#     llm=Groq(model="llama-3.1-8b-instant"),
#     system_prompt="""You are a helpful assistant that need sto provide information about FrameIQ.""",
# )




from llama_index.core.agent.workflow import ReActAgent
from llama_index.core.workflow import Context

agent = ReActAgent(
    tools=[frameiq_tool],
    llm=Groq(model="llama-3.1-8b-instant", api_key=api_key),
    system_prompt="""You are a skilled sales agent for FrameIQ, dedicated to providing detailed information 
    about our courses and persuading customers to enroll by highlighting benefits and addressing concerns. 
    Always aim to engage the customer with enthusiasm and offer personalized recommendations.""",
)

# context to hold this session/state

ctx = Context(agent)

from llama_index.core.agent.workflow import ToolCallResult, AgentStream


handler = agent.run("Tell me about FrameIQ courses.", ctx=ctx)
async for ev in handler.stream_events():
    if isinstance(ev, ToolCallResult):
        print(f"\nCall {ev.tool_name} with {ev.tool_kwargs}\nReturned: {ev.tool_output}")
    if isinstance(ev, AgentStream):
        print(f"{ev.delta}", end="", flush=True)
response = await handler
print("\nFinal Response:", str(response))


import asyncio
handler = agent.run("Tell me about FrameIQ courses.", ctx=ctx)
response = asyncio.run(handler)
print("\nFinal Response:", str(response))


