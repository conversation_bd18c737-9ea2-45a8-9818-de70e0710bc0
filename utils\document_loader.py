# Document loader utility for loading company data
import os
from typing import Dict, List

class DocumentLoader:
    """Utility class for loading and managing company documents."""
    
    def __init__(self, data_dir: str = "data/company_data"):
        self.data_dir = data_dir
    
    def load_document(self, filename: str) -> str:
        """Load a single document from the data directory."""
        filepath = os.path.join(self.data_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                return file.read()
        except FileNotFoundError:
            return f"Document {filename} not found."
        except Exception as e:
            return f"Error loading {filename}: {str(e)}"
    
    def load_all_documents(self) -> Dict[str, str]:
        """Load all documents from the data directory."""
        documents = {}
        if os.path.exists(self.data_dir):
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.txt'):
                    documents[filename] = self.load_document(filename)
        return documents
    
    def search_documents(self, query: str) -> List[Dict[str, str]]:
        """Search for a query across all documents."""
        results = []
        documents = self.load_all_documents()
        
        for filename, content in documents.items():
            if query.lower() in content.lower():
                results.append({
                    'filename': filename,
                    'content': content,
                    'relevance': content.lower().count(query.lower())
                })
        
        # Sort by relevance
        results.sort(key=lambda x: x['relevance'], reverse=True)
        return results
