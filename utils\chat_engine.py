# Chat engine for handling conversations with OpenAI
import openai
from typing import List, Dict
import config

class ChatEngine:
    """Main chat engine for handling conversations."""
    
    def __init__(self):
        openai.api_key = config.OPENAI_API_KEY
        self.conversation_history = []
    
    def add_message(self, role: str, content: str):
        """Add a message to the conversation history."""
        self.conversation_history.append({
            "role": role,
            "content": content
        })
    
    def get_response(self, user_message: str, context: str = "") -> str:
        """Get a response from OpenAI based on user message and context."""
        try:
            # Add context if provided
            if context:
                system_message = f"You are a helpful assistant for FrameIQ. Use this context to answer questions: {context}"
                messages = [{"role": "system", "content": system_message}]
            else:
                messages = [{"role": "system", "content": "You are a helpful assistant for FrameIQ."}]
            
            # Add conversation history
            messages.extend(self.conversation_history)
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=config.MAX_TOKENS,
                temperature=config.TEMPERATURE
            )
            
            assistant_message = response.choices[0].message.content
            
            # Update conversation history
            self.add_message("user", user_message)
            self.add_message("assistant", assistant_message)
            
            return assistant_message
            
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def clear_history(self):
        """Clear the conversation history."""
        self.conversation_history = []
    
    def get_history(self) -> List[Dict[str, str]]:
        """Get the current conversation history."""
        return self.conversation_history.copy()
