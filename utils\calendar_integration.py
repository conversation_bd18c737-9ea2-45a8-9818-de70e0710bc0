# Calendar integration utilities
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import config

class CalendarIntegration:
    """Integration with calendar system for scheduling."""
    
    def __init__(self):
        self.api_key = config.CALENDAR_API_KEY
        self.base_url = "https://api.calendar-system.com/v1"  # Replace with actual calendar API URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_available_slots(self, date: str, duration_minutes: int = 60) -> List[Dict]:
        """Get available time slots for a given date."""
        try:
            url = f"{self.base_url}/availability"
            params = {
                "date": date,
                "duration": duration_minutes
            }
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                return response.json().get("available_slots", [])
            else:
                return []
                
        except Exception as e:
            print(f"Error fetching available slots: {str(e)}")
            return []
    
    def schedule_meeting(self, meeting_data: Dict) -> Optional[Dict]:
        """Schedule a new meeting."""
        try:
            url = f"{self.base_url}/meetings"
            response = requests.post(url, headers=self.headers, json=meeting_data)
            
            if response.status_code == 201:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"Error scheduling meeting: {str(e)}")
            return None
    
    def get_upcoming_meetings(self, days_ahead: int = 7) -> List[Dict]:
        """Get upcoming meetings for the next specified days."""
        try:
            start_date = datetime.now().strftime("%Y-%m-%d")
            end_date = (datetime.now() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
            
            url = f"{self.base_url}/meetings"
            params = {
                "start_date": start_date,
                "end_date": end_date
            }
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                return response.json().get("meetings", [])
            else:
                return []
                
        except Exception as e:
            print(f"Error fetching upcoming meetings: {str(e)}")
            return []
    
    def cancel_meeting(self, meeting_id: str) -> bool:
        """Cancel a scheduled meeting."""
        try:
            url = f"{self.base_url}/meetings/{meeting_id}"
            response = requests.delete(url, headers=self.headers)
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"Error canceling meeting: {str(e)}")
            return False
    
    def reschedule_meeting(self, meeting_id: str, new_datetime: str) -> Optional[Dict]:
        """Reschedule an existing meeting."""
        try:
            url = f"{self.base_url}/meetings/{meeting_id}"
            update_data = {"datetime": new_datetime}
            response = requests.put(url, headers=self.headers, json=update_data)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"Error rescheduling meeting: {str(e)}")
            return None
