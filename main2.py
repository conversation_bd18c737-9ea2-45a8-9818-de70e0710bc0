"""
FrameIQ Assistant - Agentic RAG Application
A professional implementation of a Retrieval-Augmented Generation system
using ReAct agents for intelligent document querying.
"""

import os
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path

# LlamaIndex imports
from llama_index.vector_stores.faiss import FaissVectorStore
from llama_index.core import (
    VectorStoreIndex,
    load_index_from_storage,
    StorageContext,
    Settings
)
from llama_index.readers.file import PyMuPDFReader
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.llms.groq import Groq
from llama_index.core.tools import QueryEngineTool
from llama_index.core.agent.workflow import ReActAgent
from llama_index.core.workflow import Context
from llama_index.core.agent.workflow import ToolCallResult, AgentStream

import faiss


class FrameIQAssistant:
    """
    A professional RAG-based assistant for FrameIQ course information.
    Utilizes FAISS vector store and ReAct agents for intelligent querying.
    """
    
    def __init__(
        self,
        pdf_path: str = "FrameIQ dummy.pdf",
        storage_dir: str = "storage",
        embedding_model: str = "BAAI/bge-small-en-v1.5",
        llm_model: str = "llama-3.1-8b-instant",
        embedding_dim: int = 384,
        similarity_top_k: int = 3
    ):
        """
        Initialize the FrameIQ Assistant.
        
        Args:
            pdf_path: Path to the FrameIQ PDF document
            storage_dir: Directory for persisting vector store
            embedding_model: HuggingFace embedding model name
            llm_model: Groq LLM model name
            embedding_dim: Dimension of embeddings
            similarity_top_k: Number of similar documents to retrieve
        """
        self.pdf_path = pdf_path
        self.storage_dir = Path(storage_dir)
        self.embedding_model_name = embedding_model
        self.llm_model_name = llm_model
        self.embedding_dim = embedding_dim
        self.similarity_top_k = similarity_top_k
        
        # Initialize API key
        self._setup_api_keys()
        
        # Initialize models
        self._initialize_models()
        
        # Initialize or load index
        self.index = self._setup_vector_store()
        
        # Setup query engine and agent
        self.query_engine = self._create_query_engine()
        self.agent = self._create_agent()
        
    def _setup_api_keys(self) -> None:
        """Configure API keys from environment variables."""
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            raise ValueError("GROQ_API_KEY environment variable not set")
        os.environ["GROQ_API_KEY"] = groq_api_key
        
    def _initialize_models(self) -> None:
        """Initialize embedding and language models."""
        # Initialize embedding model
        self.embed_model = HuggingFaceEmbedding(
            model_name=self.embedding_model_name
        )
        
        # Initialize LLM
        self.llm = Groq(
            model=self.llm_model_name,
            api_key=os.environ["GROQ_API_KEY"]
        )
        
        # Set global settings
        Settings.embed_model = self.embed_model
        Settings.llm = self.llm
        
    def _setup_vector_store(self) -> VectorStoreIndex:
        """
        Setup or load the vector store index.
        
        Returns:
            VectorStoreIndex: The loaded or created index
        """
        # Check if storage already exists
        if self.storage_dir.exists() and any(self.storage_dir.iterdir()):
            print("Loading existing vector store...")
            return self._load_existing_index()
        else:
            print("Creating new vector store...")
            return self._create_new_index()
            
    def _load_existing_index(self) -> VectorStoreIndex:
        """Load index from existing storage."""
        vector_store = FaissVectorStore.from_persist_dir(str(self.storage_dir))
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            persist_dir=str(self.storage_dir)
        )
        
        index = load_index_from_storage(
            storage_context=storage_context,
            embed_model=self.embed_model
        )
        
        return index
        
    def _create_new_index(self) -> VectorStoreIndex:
        """Create a new index from PDF documents."""
        # Load documents
        loader = PyMuPDFReader()
        documents = loader.load(file_path=self.pdf_path)
        
        # Create FAISS index
        faiss_index = faiss.IndexFlatL2(self.embedding_dim)
        vector_store = FaissVectorStore(faiss_index=faiss_index)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        
        # Create index from documents
        index = VectorStoreIndex.from_documents(
            documents,
            storage_context=storage_context,
            embed_model=self.embed_model,
            show_progress=True
        )
        
        # Persist to disk
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        index.storage_context.persist(persist_dir=str(self.storage_dir))
        
        return index
        
    def _create_query_engine(self):
        """Create a query engine from the index."""
        return self.index.as_query_engine(
            llm=self.llm,
            similarity_top_k=self.similarity_top_k
        )
        
    def _create_agent(self) -> ReActAgent:
        """Create the ReAct agent with FrameIQ tool."""
        # Create FrameIQ tool
        frameiq_tool = QueryEngineTool.from_defaults(
            query_engine=self.query_engine,
            name="frameiq_knowledge_base",
            description=(
                "A comprehensive knowledge base containing detailed information about FrameIQ courses, "
                "programs, pricing, schedules, and benefits. Use this tool to answer any questions "
                "about FrameIQ's educational offerings."
            )
        )
        
        # Enhanced system prompt for better structured output
        system_prompt = """You are an expert FrameIQ course advisor and sales consultant. Your role is to:

1. Provide accurate, detailed information about FrameIQ courses and programs
2. Highlight key benefits and unique features of our offerings
3. Address customer concerns professionally and persuasively
4. Guide potential students towards enrollment decisions

IMPORTANT INSTRUCTIONS:
- Always use the frameiq_knowledge_base tool to retrieve accurate information
- Structure your responses clearly with relevant details
- Be enthusiastic but professional
- Focus on value proposition and student success outcomes
- If asked about specific course details, provide comprehensive information
- Always maintain a helpful and consultative approach

Remember: You represent FrameIQ's commitment to educational excellence."""
        
        # Create agent
        agent = ReActAgent(
            tools=[frameiq_tool],
            llm=self.llm,
            system_prompt=system_prompt,
            verbose=False  # Disable verbose output to hide CoT
        )
        
        return agent
        
    async def query_async(self, question: str, context: Optional[Context] = None) -> str:
        """
        Query the assistant asynchronously.
        
        Args:
            question: User's question
            context: Optional context for maintaining conversation state
            
        Returns:
            str: Assistant's response
        """
        if context is None:
            context = Context(self.agent)
            
        # Run the agent
        handler = self.agent.run(question, ctx=context)
        
        # Collect only the final response
        final_response = ""
        async for ev in handler.stream_events():
            if isinstance(ev, AgentStream):
                final_response += ev.delta
                
        await handler
        
        return final_response.strip()
        
    def query(self, question: str, context: Optional[Context] = None) -> str:
        """
        Synchronous wrapper for querying the assistant.
        
        Args:
            question: User's question
            context: Optional context for maintaining conversation state
            
        Returns:
            str: Assistant's response
        """
        return asyncio.run(self.query_async(question, context))
        

class FrameIQChatInterface:
    """
    Interactive chat interface for FrameIQ Assistant.
    """
    
    def __init__(self, assistant: FrameIQAssistant):
        """
        Initialize chat interface.
        
        Args:
            assistant: FrameIQAssistant instance
        """
        self.assistant = assistant
        self.context = Context(assistant.agent)
        
    async def start_chat(self):
        """Start the interactive chat session."""
        print("=" * 60)
        print("Welcome to FrameIQ Assistant!")
        print("I'm here to help you learn about our courses and programs.")
        print("Type 'exit' or 'quit' to end the conversation.")
        print("=" * 60)
        print()
        
        while True:
            # Get user input
            user_input = input("User: ").strip()
            
            # Check for exit commands
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("\nFrameIQ Assistant: Thank you for your interest in FrameIQ! "
                      "Feel free to reach out if you have more questions. Goodbye!")
                break
                
            # Skip empty inputs
            if not user_input:
                continue
                
            # Get response from assistant
            print("\nFrameIQ Assistant: ", end="", flush=True)
            
            try:
                response = await self.assistant.query_async(user_input, self.context)
                print(response)
            except Exception as e:
                print(f"I apologize, but I encountered an error: {str(e)}")
                print("Please try rephrasing your question.")
                
            print()  # Add spacing between interactions
            

def main():
    """Main entry point for the application."""
    try:
        # Initialize the assistant
        print("Initializing FrameIQ Assistant...")
        assistant = FrameIQAssistant()
        
        # Create chat interface
        chat = FrameIQChatInterface(assistant)
        
        # Start interactive chat
        asyncio.run(chat.start_chat())
        
    except Exception as e:
        print(f"Error initializing assistant: {str(e)}")
        raise


if __name__ == "__main__":
    main()
